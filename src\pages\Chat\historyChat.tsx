import { ASSISTANT_ROLE, USER_ROLE } from '@/config/chat';
import { getHistoryChatList } from '@/services/chat';
import { dispatchInUtils, getState } from '@/utils';
import type { Message } from '@douyinfe/semi-ui/lib/es/chat/interface';
import React, { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useLocation, useParams, useSelector } from 'umi';
import AIChat from './Chat';
import type { ChatInstanceRef, ChatListResult, ChatMessageContent, QuestionsData } from './types';

import { NEED_RECONNECT_APPS } from '@/config';
import type { ChatContent } from '@/models/chat';
import { PdfContainerModelState } from '@/models/pdfContainer';
import VditorEditor from '@/pages/Chat/components/Vditor';
import { mergeByCollectionId } from '@/utils';
import classNames from 'classnames';
import { useOfficeTemplate } from '../Office/hooks/useOfficeTemplate';
import IframePPT from './components/IframePPT';
import PdfContainer from './components/PdfContainer';
import PdfViewer from './components/PdfViewer';
import styles from './index.less';

const ChatPage: React.FC = () => {
  const { chatId } = useParams<{ chatId: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const appCode = searchParams.get('appCode') || '';
  const type = searchParams.get('type') || ''; // 是否来自Office页面
  const chatRef = useRef<ChatInstanceRef>(null);
  const location = useLocation();
  const routeId = (location.state as QuestionsData)?.routeId;

  const [isFromOffice, setIsFromOffice] = useState(false);
  const { clearRichTextState } = useOfficeTemplate();
  interface MsgType {
    type: 'text' | 'reasoning' | 'image_url' | 'file_url' | 'ppt' | 'chat_json' | 'params_json';
    text: string;
    reasoningContent?: string;
    image_url?: { url: string; [x: string]: any };
    file_url?: { url: string; [x: string]: any };
    ratio?: string;
  }
  enum MessageType {
    text = 'text',
    ppt = 'ppt',
    chat_json = 'chat_json',
    reasoning = 'reasoning',
    image = 'image_url',
    file = 'file_url',
    params_json = 'params_json',
  }
  const normalizeChatData = (messages: ChatMessageContent[]) => {
    let reasoningContent = '';
    const content: Message['content'][] = [];
    let pptDetail = {};
    let contentJson = {};
    let paramsJson = {};
    messages.forEach((msg) => {
      const temp: MsgType = {
        type: MessageType[msg.type],
        text: '',
      };
      if (msg.type === 'text') {
        temp.text += msg?.text?.content;
      }
      if (msg.type === 'reasoning') {
        reasoningContent += msg?.reasoning?.content;
      }
      if (msg.type === 'ppt') {
        pptDetail = msg?.ppt || {};
      }
      if (msg.type === 'chat_json') {
        contentJson = JSON.parse(msg?.chatJson?.content || '{}');
      }
      if (msg.type === 'params_json') {
        paramsJson = JSON.parse(msg?.paramsJson?.content || '{}');
      }
      const isFile = msg.type === 'file';
      const { name, size, url, type, fileTags, width, height } = msg.file || {};
      if (isFile && type === 'image') {
        temp.type = 'image_url';
        temp.image_url = {
          url: url || '',
          name: name || '',
          size: size || '',
          type: msg.type,
          width,
          height,
        };
        // if (width && height) {
        // 通过宽高计算出来当前图片的 宽高比例是多少  例如 1920 1080 = 9:16
        // const gcd = (a: number, b: number): number => {
        //   return b === 0 ? a : gcd(b, a % b);
        // };
        // const ratio = gcd(width, height);
        // const aspectRatio = `${width / ratio}:${height / ratio}`;
        // temp.ratio = aspectRatio;
        // }
      }
      if (isFile && type === 'file') {
        temp.type = 'file_url';
        temp.file_url = {
          url: url || '',
          name: name || '',
          size: size || '',
          type: msg.type,
          fileTags: fileTags || [],
        };
      }
      content.push(temp as unknown as Message['content']);
    });
    return {
      content,
      pptDetail,
      contentJson,
      reasoningContent,
      paramsJson,
    };
  };

  const dooHandleChatsChange = (chatList: Message[]) => {
    const pagemode = getState()?.pageLayout.mode;
    if (pagemode === 'doc' && chatList) {
      for (let ii = 0; ii < chatList.length; ii++) {
        const aChatList = chatList[ii];
        let findafile = false;
        for (let i = 0; i < (aChatList.content || []).length; i++) {
          let atmpitem = aChatList.content?.[i] as ChatContent;
          let afileUrl =
            atmpitem && atmpitem.file_url && atmpitem.file_url.url ? atmpitem.file_url.url : '';

          let fileExt = '';
          if (afileUrl) {
            let exttmps = afileUrl.split('?')[0].split('.');
            fileExt = exttmps[exttmps.length - 1];
          }

          if (fileExt && ['pdf', 'doc', 'docx', 'ppt', 'pptx'].indexOf(fileExt) > -1) {
            dispatchInUtils({
              type: 'pdfContainer/changeUrl',
              payload: {
                url: afileUrl,
                name: atmpitem.file_url?.name,
                size: atmpitem.file_url?.size,
              },
            });

            findafile = true;
            break;
          }
        }

        if (findafile) break;
      }
    }

    if (pagemode === 'write' && chatList) {
      let lastContent = '';
      if (chatList) {
        chatList.forEach((it) => {
          if (it.role !== ASSISTANT_ROLE) return;
          if (!it.content || !it.content[0] || (it.content[0] as ChatContent)?.type !== 'text')
            return;
          lastContent = (it.content[0] as ChatContent)?.text || '';
        });
      }

      if (lastContent) {
        setTimeout(() => {
          dispatchInUtils({
            type: 'aiWrite/setContent',
            payload: {
              content: lastContent,
            },
          });
        }, 1000);
      }
    }
    chatRef.current?.handleChatsChange(chatList);
  };

  const fetchHistoryList = () => {
    getHistoryChatList({
      appCode,
      chatId,
      offSet: 0,
      pageSize: 1024,
    }).then((res) => {
      const data = res?.data?.data?.list || [];
      const Role = {
        Human: USER_ROLE,
        AI: ASSISTANT_ROLE,
      };

      const isNeedReconnect = NEED_RECONNECT_APPS.includes(appCode);

      if (data?.length) {
        const chatList = data.map((item: any, index: number) => {
          // console.log(item, 'item');
          const { content, pptDetail, reasoningContent, contentJson, paramsJson } =
            normalizeChatData(item.value);

          const result: ChatListResult = {
            id: item.dataId,
            content,
            reasoningContent,
            paramsJson,
            role: Role[item.obj as 'Human' | 'AI'] || '',
            like:
              (typeof item.userGoodFeedback === 'string' && item.userGoodFeedback === 'yes') ||
              item.userGoodFeedback,
            dislike:
              (typeof item.userBadFeedback === 'string' && item.userBadFeedback === 'yes') ||
              item.userBadFeedback,
            ...(item.obj === 'AI' && { quoteList: mergeByCollectionId(item.quote || []) }),
          };
          if (appCode === 'ppt' && item.obj === 'AI') {
            result.pptDetail = pptDetail;
          }
          if (item.obj === 'AI' && contentJson) {
            result.contentJson = contentJson;
          }
          // 如果是最后一个元素，并且是AI, 并且value的length为0, 则重新建立链接
          if (isNeedReconnect && index === data.length - 1 && item.obj === 'AI') {
            result.isReconnect = item.value?.length === 0;
          }
          return result;
        });

        // 循环 chatList 判断 id 相等的
        // console.log(chatList, 'chatList');
        dooHandleChatsChange(chatList as unknown as Message[]);
        /*
        if (getState()?.pageLayout.mode === 'doc' && chatList) {
          for (let ii = 0; ii < chatList.length; ii++) {
            let achatList = chatList[ii];
            let findafile = false;
            for (let i = 0; i < achatList.content.length; i++) {
              let atmpitem = achatList.content[i];
              let afileUrl =
                atmpitem && atmpitem.file_url && atmpitem.file_url.url ? atmpitem.file_url.url : '';

              if (
                afileUrl &&
                (afileUrl.split('?')[0].endsWith('.pdf') ||
                  afileUrl.split('?')[0].endsWith('.doc') ||
                  afileUrl.split('?')[0].endsWith('.docx'))
              ) {
                dispatchInUtils({
                  type: 'pdfContainer/changeUrl',
                  payload: {
                    url: afileUrl,
                    name: atmpitem.file_url.name,
                    size: atmpitem.file_url.size,
                  },
                });

                findafile = true;
                break;
              }
            }

            if (findafile) break;
          }
        }
        chatRef.current?.handleChatsChange(chatList as unknown as Message[]);
        */
      }
    });
  };

  useEffect(() => {
    let qpagemode = searchParams.get('pagemode');

    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: qpagemode ? qpagemode : '',
    });

    dispatchInUtils({
      type: 'pageLayout/changeChatContainerHide',
      payload: '',
    });
    dispatchInUtils({
      type: 'aiPPT/hidePpt',
    });
    dispatchInUtils({ type: 'aiPPT/clearPpt' });

    // 监听Chat完成事件，更新历史记录
    const handleChatUpdate = (data: any) => {
      if (data.chatId === chatId) {
        fetchHistoryList();
      }
    };

    // eventBus.on('Chat-History-UpdateList', handleChatUpdate);

    // 新对话跳转
    if (chatId) {
      const { fromOffice = false } = (location.state as QuestionsData) || {};
      setIsFromOffice(fromOffice);
      if (!appCode && location.state) {
        const { content, attachments, imgConfig } = (location.state as QuestionsData) || {};
        chatRef.current?.clearMessages();
        const customAttachments =
          typeof attachments === 'string' ? JSON.parse(attachments) : attachments;
        if (content || customAttachments?.length || imgConfig?.configText) {
          chatRef.current?.handleMessageSend(content, customAttachments);
          const newParams = new URLSearchParams(searchParams);
          newParams.set('appCode', type || 'chat');
          newParams.delete('type');
          setSearchParams(newParams, { state: location.state });
        }
        return;
      }
      if (!getState()?.chat.chatMsg![chatId]) {
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            appCode,
            [chatId]: {
              messages: [],
              pending: false,
            },
          },
        });
        const chatMsg = getState()?.chat.chatMsg;
        const ChatKeys = Object.keys(chatMsg);
        if (ChatKeys.length > 5) {
          // 删除缓存的chatId
          dispatchInUtils({
            type: 'chat/deleteChatId',
            payload: {
              chatId: ChatKeys[0],
            },
          });
        }
        fetchHistoryList();
      } else {
        dooHandleChatsChange(getState()?.chat.chatMsg![chatId]?.messages);
        // image应用下，要更新最新的历史对话
        if (['image'].includes(appCode)) {
          fetchHistoryList();
        }
      }
    }

    return () => {
      clearRichTextState();

      // 清理事件监听器
      // eventBus.off('Chat-History-UpdateList', handleChatUpdate);

      dispatchInUtils({
        type: 'pdfContainer/changeUrl',
        payload: {
          url: '',
          name: '',
          size: '',
        },
      });

      dispatchInUtils({
        type: 'pageLayout/changePageMode',
        payload: '',
      });
    };
  }, [chatId, JSON.stringify(location.state)]);

  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );
  const { url: pdfUrl, name: pdfName } = useSelector(
    (state: { pdfContainer: PdfContainerModelState }) => state.pdfContainer,
  );

  const pageShowtrans: boolean = useSelector(
    (state: { pageLayout: { showtrans: false } }) => state.pageLayout.showtrans,
  );

  const pageTranshowori: boolean = useSelector(
    (state: { pageLayout: { transhowori: false } }) => state.pageLayout.transhowori,
  );

  const chatContainerHide: boolean = useSelector(
    (state: { pageLayout: { chatContainerHide: false } }) => state.pageLayout.chatContainerHide,
  );

  const getChatWrapStyle = function () {
    let s: Record<string, any> = {};
    if (['doc', 'write'].indexOf(pageMode) > -1) s.paddingTop = 60;
    if (chatContainerHide) s.display = 'none';
    return s;
  };

  const getPdfWrapClassName = function () {
    let s = [styles.historyWrapIn];
    if (pageShowtrans && pageTranshowori) s.push(styles.historyWrapInTrans);

    return s.join(' ');
  };

  //let navigate = useNavigate();
  // content: string, attachment: []
  const handleSend = () => {
    /*
    const stateData = {
      content,
      attachments: JSON.stringify(attachment),
      fromOffice: true,
    };
    if (appCode === 'write') {
      const queryString = window.location.search;
      navigate(`/chat-read/${chatId}?${queryString}`, {
        state: stateData,
      });
    }
    */

    if (appCode === 'write') {
      dispatchInUtils({
        type: 'pageLayout/changePageMode',
        payload: 'write',
      });
    }
  };

  const rightContainer = () => {
    if (pageMode === 'doc') {
      return (
        <div className={getPdfWrapClassName()}>
          <PdfContainer chatId={chatId} appCode={appCode || type} />
        </div>
      );
    }

    if (pageMode === 'ppt') {
      return <PdfViewer name={pdfName} url={pdfUrl} />;
    }

    if (pageMode === 'write') {
      return (
        <div className={styles.historyWrapIn}>
          <VditorEditor
            chatId={chatId}
            height={'95vh'}
            placeholder="请输入内容..."
            saveUrl="/api/content/save"
            saveMethod="POST"
          />
        </div>
      );
    }

    return <></>;
  };

  return (
    <div className={styles.historyWrap}>
      <div
        className={classNames([styles.historyWrapIn, styles.historyMain])}
        style={getChatWrapStyle()}
      >
        <AIChat
          ref={chatRef}
          appCode={appCode || type}
          showTemplateHeader={isFromOffice}
          routeId={routeId}
          customHandleSendReading={handleSend}
        />
      </div>
      {rightContainer()}

      {/* AIPPT生成 */}
      <IframePPT />
    </div>
  );
};

export default ChatPage;
